﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{91E27CEB-BC9E-3534-B028-4D7213B8740B}"
	ProjectSection(ProjectDependencies) = postProject
		{CC86A599-4D70-3328-BFAF-48A359216030} = {CC86A599-4D70-3328-BFAF-48A359216030}
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7} = {EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D} = {24211F28-81C7-33A7-AF6C-C6531BFF7B7D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{CC86A599-4D70-3328-BFAF-48A359216030}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "property", "property.vcxproj", "{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}"
	ProjectSection(ProjectDependencies) = postProject
		{CC86A599-4D70-3328-BFAF-48A359216030} = {CC86A599-4D70-3328-BFAF-48A359216030}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_property", "test_property.vcxproj", "{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}"
	ProjectSection(ProjectDependencies) = postProject
		{CC86A599-4D70-3328-BFAF-48A359216030} = {CC86A599-4D70-3328-BFAF-48A359216030}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{91E27CEB-BC9E-3534-B028-4D7213B8740B}.Debug|x64.ActiveCfg = Debug|x64
		{91E27CEB-BC9E-3534-B028-4D7213B8740B}.Release|x64.ActiveCfg = Release|x64
		{91E27CEB-BC9E-3534-B028-4D7213B8740B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{91E27CEB-BC9E-3534-B028-4D7213B8740B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.Debug|x64.ActiveCfg = Debug|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.Debug|x64.Build.0 = Debug|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.Release|x64.ActiveCfg = Release|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.Release|x64.Build.0 = Release|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CC86A599-4D70-3328-BFAF-48A359216030}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.Debug|x64.ActiveCfg = Debug|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.Debug|x64.Build.0 = Debug|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.Release|x64.ActiveCfg = Release|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.Release|x64.Build.0 = Release|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EEFAE1A4-CF7B-3C56-A913-09927FD6C5A7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.Debug|x64.ActiveCfg = Debug|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.Debug|x64.Build.0 = Debug|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.Release|x64.ActiveCfg = Release|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.Release|x64.Build.0 = Release|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{24211F28-81C7-33A7-AF6C-C6531BFF7B7D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E1868C1C-EF1A-32F1-AD32-677A69BE7C83}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
