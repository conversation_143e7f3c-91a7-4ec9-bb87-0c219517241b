^E:\PROJECTS\CODE\AI AGENT\PROPERTY\BUILD\CMAKEFILES\CCFEB955051A5C87D37B2944C8A8D990\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SE:/Projects/Code/AI Agent/property" "-BE:/Projects/Code/AI Agent/property/build" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "E:/Projects/Code/AI Agent/property/build/property.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
