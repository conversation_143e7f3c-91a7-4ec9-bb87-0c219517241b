#ifndef PROPERTY_H
#define PROPERTY_H

#include <cassert>
#include <source_location>
#include <concepts>
#include <string_view>

#include "global.h"
#include "propertyprivate.h"
#include "shareddata.h"
#include "taggedpointer.h"

/**
 * @brief 现代C++20 concepts定义
 *
 * 这些concepts用于替换传统的SFINAE技术，提供更清晰的模板约束表达。
 */
namespace PropertyConcepts {
    template<typename T, typename U>
    concept ForwardConstructible = requires {
        requires std::is_same_v<std::decay_t<U>, T> == false;
        requires std::is_constructible_v<T, U>;
    };

    template<typename Functor, typename T>
    concept InvocableReturning = std::is_invocable_r_v<T, Functor&>;
}

/**
 * @brief 源代码位置支持
 *
 * 这些宏定义用于检查和引入 C++20 的 std::source_location 或实验性的
 * std::experimental::source_location，以便在属性绑定时收集源代码位置信息。
 */
#if __has_include(<source_location>) && __cplusplus >= 202002L
#include <source_location>
#if defined(__cpp_lib_source_location)
#define SOURCE_LOCATION_NAMESPACE std
#define PROPERTY_COLLECT_BINDING_LOCATION
#if defined(_MSC_VER)
/* MSVC runs into an issue with constexpr with source location (error C7595)
   so use the factory function as a workaround */
#  define PROPERTY_DEFAULT_BINDING_LOCATION PropertyBindingSourceLocation::fromStdSourceLocation(std::source_location::current())
#else
/* some versions of gcc in turn run into
   expression ‘std::source_location::current()’ is not a constant expression
   so don't use the workaround there */
#  define PROPERTY_DEFAULT_BINDING_LOCATION PropertyBindingSourceLocation(std::source_location::current())
#endif
#endif
#endif

// TODO_REMOVE: 实验性源位置支持 - 核心API未使用实验性source_location
/*
#if __has_include(<experimental/source_location>)
#include <experimental/source_location>
#if !defined(QT_PROPERTY_COLLECT_BINDING_LOCATION)
#if defined(__cpp_lib_experimental_source_location)
#define QT_SOURCE_LOCATION_NAMESPACE std::experimental
#define QT_PROPERTY_COLLECT_BINDING_LOCATION
#define QT_PROPERTY_DEFAULT_BINDING_LOCATION PropertyBindingSourceLocation(std::experimental::source_location::current())
#endif // defined(__cpp_lib_experimental_source_location)
#endif
#endif
*/

#if !defined(PROPERTY_COLLECT_BINDING_LOCATION)
#define PROPERTY_DEFAULT_BINDING_LOCATION PropertyBindingSourceLocation()
#endif

/**
 * @brief 属性更新组
 *
 * 标记属性更新组的开始。在该组中，更改一个属性既不会立即更新任何从属属性，
 * 也不会触发更改通知。更改通知将被延迟，直到调用endPropertyUpdateGroup 结束该组。
 */
void beginPropertyUpdateGroup();

/**
 * @brief 结束一个属性更新组
 *
 * 如果最外层组已结束，则现在进行延迟绑定评估和通知。
 */
void endPropertyUpdateGroup();

/**
 * @brief ScopedPropertyUpdateGroup 类用于管理属性更新组的生命周期
 *
 * 构造函数调用 beginPropertyUpdateGroup，析构函数调用 endPropertyUpdateGroup，
 * 确保属性更新组在作用域结束时正确结束。
 */
class ScopedPropertyUpdateGroup
{
    DISABLE_COPY_MOVE(ScopedPropertyUpdateGroup)
public:
    [[nodiscard]]
    ScopedPropertyUpdateGroup()
    { beginPropertyUpdateGroup(); }
    ~ScopedPropertyUpdateGroup() noexcept(false)
    { endPropertyUpdateGroup(); }
};

/**
 * @brief 属性数据模板类
 *
 * PropertyData<T> 是一个通用基类，用于保存具有自动数据绑定的属性。
 * 它主要封装存储的数据(val成员变量)，并提供对这些数据的低级访问。
 * 它根据类型特征决定是否使用引用传递，并提供了值的存取接口。
 *
 * @warning 该类提供的对数据的低级访问绕过了绑定机制，使用时应小心谨慎，
 *          因为值的更新不会传播到依赖于该属性的任何绑定中。
 *
 * @tparam T 属性值的类型
 *
 * TODO_SIMPLIFY: PropertyData<T>与Property<T>合并优化方案
 *
 * 当前架构分析：
 * - PropertyData<T>: 负责值存储、类型特征处理、绕过绑定的直接访问
 * - Property<T>: 继承PropertyData<T>，添加绑定管理、变化通知、依赖跟踪
 *
 * 合并收益：
 * - 简化类层次结构，减少一层继承开销
 * - 统一属性接口，提高API一致性
 * - 减少模板实例化复杂度
 *
 * 技术风险：
 * - 可能违反单一职责原则（值存储 vs 绑定管理）
 * - 需要重新设计绕过绑定的访问接口
 * - 影响ObjectCompatProperty等依赖PropertyData的类
 *
 * 实施建议：
 * - 先评估ObjectCompatProperty的重构成本
 * - 考虑保留PropertyData作为轻量级基类的选项
 * - 分阶段合并：先合并接口，再合并实现
 */
template <typename T>
class PropertyData : public UntypedPropertyData
{
protected:
    mutable T val = T();
protected:
    static constexpr bool UseReferences =
        !(std::is_arithmetic_v<T> || std::is_enum_v<T> || std::is_pointer_v<T>);
public:
    using value_type = T;
    using parameter_type = std::conditional_t<UseReferences, const T &, T>;
    using rvalue_ref = std::conditional_t<UseReferences, T &&, T>;
    using arrow_operator_result = std::conditional_t<std::is_pointer_v<T>, const T &,
                                                     std::conditional_t<TypeTraits::is_dereferenceable_v<T>, const T &, void>>;

    PropertyData() = default;
    PropertyData(parameter_type t) : val(t) {}
    template<typename U> requires (UseReferences && PropertyConcepts::ForwardConstructible<T, U>)
    PropertyData(U&& t) : val(std::forward<U>(t)) {}
    ~PropertyData() = default;

    parameter_type valueBypassingBindings() const { return val; }
    void setValueBypassingBindings(parameter_type v) { val = v; }
    template<typename U> requires (UseReferences && PropertyConcepts::ForwardConstructible<T, U>)
    void setValueBypassingBindings(U&& v) { val = std::forward<U>(v); }
};

/**
 * @brief 属性绑定源位置
 *
 * PropertyBindingSourceLocation 结构体用于存储属性绑定的源代码位置信息。
 * 它可以从 std::source_location 初始化。
 */
struct PropertyBindingSourceLocation
{
    const char *fileName = nullptr;
    const char *functionName = nullptr;
    uint32_t line = 0;
    uint32_t column = 0;
    PropertyBindingSourceLocation() = default;
#ifdef __cpp_lib_source_location
    constexpr PropertyBindingSourceLocation(const std::source_location &cppLocation)
    {
        fileName = cppLocation.file_name();
        functionName = cppLocation.function_name();
        line = cppLocation.line();
        column = cppLocation.column();
    }
    static consteval PropertyBindingSourceLocation
    fromStdSourceLocation(const std::source_location &cppLocation)
    {
        return cppLocation;
    }
#endif
// TODO_REMOVE: 实验性源位置构造函数 - 与上面实验性支持一致，核心API未使用
/*
#ifdef __cpp_lib_experimental_source_location
    constexpr PropertyBindingSourceLocation(const std::source_location &cppLocation)
    {
        fileName = cppLocation.file_name();
        functionName = cppLocation.function_name();
        line = cppLocation.line();
        column = cppLocation.column();
    }
#endif
*/
};

/**
 * @brief 属性绑定错误类
 *
 * PropertyBindingError 类用于处理属性绑定过程中的错误。
 * 它定义了几种错误类型，简化了原有的pImpl模式实现。
 *
 * @note 优化说明：移除了SharedDataPointer的pImpl模式，直接存储错误信息，
 *       减少了内存分配和间接访问的开销。
 */
template <typename Functor> class PropertyChangeHandler;

class PropertyBindingError {
public:
    enum Type {
        NoError,
        BindingLoop,
        EvaluationError,
        UnknownError
    };

    PropertyBindingError() = default;
    PropertyBindingError(Type type, std::string_view description = "");

    PropertyBindingError(const PropertyBindingError &other) = default;
    PropertyBindingError &operator=(const PropertyBindingError &other) = default;
    PropertyBindingError(PropertyBindingError &&other) = default;
    PropertyBindingError &operator=(PropertyBindingError &&other) = default;
    ~PropertyBindingError() = default;

    bool hasError() const { return m_type != NoError; }
    Type type() const { return m_type; }
    std::string description() const { return m_description; }

private:
    Type m_type = NoError;
    std::string m_description;
};

/**
 * @brief 未类型化的属性绑定
 *
 * UntypedPropertyBinding 类实现了属性之间的绑定关系。
 * 它支持绑定函数表(vtable)的概念，并通过模板函数参数推导实现灵活的绑定创建。
 *
 * TODO_MERGE_CANDIDATE: UntypedPropertyBinding与PropertyBinding<T>合并候选
 * - UntypedPropertyBinding主要用于类型擦除和统一接口
 * - PropertyBinding<T>继承UntypedPropertyBinding并提供类型安全的接口
 * - 合并的挑战：需要重新设计类型擦除机制
 * - 合并的收益：简化绑定类层次，减少虚函数调用开销
 * - 风险评估：较高，需要重构整个绑定系统的类型处理
 */
class UntypedPropertyBinding
{
public:
    UntypedPropertyBinding();
    UntypedPropertyBinding(const BindingFunctionVTable *vtable, void *function, const PropertyBindingSourceLocation &location);

    template<typename Functor>
    UntypedPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location)
        : UntypedPropertyBinding(&bindingFunctionVTable<std::remove_reference_t<Functor>>, &f, location)
    {}

    UntypedPropertyBinding(UntypedPropertyBinding &&other);
    UntypedPropertyBinding(const UntypedPropertyBinding &other);
    UntypedPropertyBinding &operator=(const UntypedPropertyBinding &other);
    UntypedPropertyBinding &operator=(UntypedPropertyBinding &&other);
    ~UntypedPropertyBinding();

    bool isNull() const;

    PropertyBindingError error() const;

    explicit UntypedPropertyBinding(PropertyBindingPrivate *priv);
private:
    friend class PropertyBindingData;
    friend class PropertyBindingPrivate;
    template <typename> friend class PropertyBinding;
    PropertyBindingPrivatePtr d;
};

/**
 * @brief 类型化的属性绑定
 *
 * PropertyBinding 类继承自 UntypedPropertyBinding，并为特定类型的属性提供绑定功能。
 *
 * @tparam PropertyType 属性的类型
 */
template <typename PropertyType>
class PropertyBinding : public UntypedPropertyBinding
{

public:
    PropertyBinding() = default;

    template<typename Functor>
    PropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location)
        : UntypedPropertyBinding(&bindingFunctionVTable<std::remove_reference_t<Functor>, PropertyType>, &f, location)
    {}

    // TODO_SIMPLIFY: 内部转换构造函数 - 用于类型转换，保留以确保编译性
    // Internal
    explicit PropertyBinding(const UntypedPropertyBinding &binding)
        : UntypedPropertyBinding(binding)
    {}
};

template <typename Functor>
auto makePropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION,
                         std::enable_if_t<std::is_invocable_v<Functor>> * = nullptr)
{
    return PropertyBinding<std::invoke_result_t<Functor>>(std::forward<Functor>(f), location);
}

/**
 * @brief 属性观察者
 *
 * PropertyObserverBase 和 PropertyObserver 类用于实现属性的观察者模式，
 * 允许在属性值变化时通知绑定或处理器。
 */
struct PropertyObserverPrivate;
struct PropertyObserverPointer;
class PropertyObserver;

class PropertyObserverBase
{
public:
    // Internal
    enum ObserverTag {
        ObserverNotifiesBinding, // observer was installed to notify bindings that obsverved property changed
        ObserverNotifiesChangeHandler, // observer is a change handler, which runs on every change
        ObserverIsPlaceholder  // the observer before this one is currently evaluated in QPropertyObserver::notifyObservers.
    };
protected:
    using ChangeHandler = void (*)(PropertyObserver*, UntypedPropertyData *);

private:
    friend struct PropertyDelayedNotifications;
    friend struct PropertyObserverNodeProtector;
    friend class PropertyObserver;
    friend struct PropertyObserverPointer;
    friend struct PropertyBindingDataPointer;
    friend class PropertyBindingPrivate;

    TaggedPointer<PropertyObserver, ObserverTag> next;
    // prev is a pointer to the "next" element within the previous node, or to the "firstObserverPtr" if it is the
    // first node.
    TagPreservingPointerToPointer<PropertyObserver, ObserverTag> prev;

    union {
        PropertyBindingPrivate *binding = nullptr;
        ChangeHandler changeHandler;
        UntypedPropertyData *aliasData;
    };
};

class PropertyObserver : public PropertyObserverBase
{
public:
    constexpr PropertyObserver() = default;
    PropertyObserver(PropertyObserver &&other) noexcept;
    PropertyObserver &operator=(PropertyObserver &&other) noexcept;
    ~PropertyObserver();

    template <typename Property, IsUntypedPropertyData<Property> = true>
    void setSource(const Property &property)
    { setSource(property.bindingData()); }
    void setSource(const PropertyBindingData &property);

protected:
    PropertyObserver(ChangeHandler changeHandler);

    UntypedPropertyData *aliasedProperty() const
    {
        return aliasData;
    }

private:

    PropertyObserver(const PropertyObserver &) = delete;
    PropertyObserver &operator=(const PropertyObserver &) = delete;

};

/**
 * @brief 属性变化处理器和通知器
 *
 * PropertyChangeHandler 和 PropertyNotifier 类用于处理属性值变化时的回调和通知。
 */
template <typename Functor>
class PropertyChangeHandler : public PropertyObserver
{
    Functor m_handler;
public:
    [[nodiscard]]
    PropertyChangeHandler(Functor handler)
        : PropertyObserver([](PropertyObserver *self, UntypedPropertyData *) {
            auto This = static_cast<PropertyChangeHandler<Functor>*>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
    }

    template <typename Property, IsUntypedPropertyData<Property> = true>
    [[nodiscard]]
    PropertyChangeHandler(const Property &property, Functor handler)
        : PropertyObserver([](PropertyObserver *self, UntypedPropertyData *) {
            auto This = static_cast<PropertyChangeHandler<Functor>*>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
        setSource(property);
    }
};

class PropertyNotifier : public PropertyObserver
{
    std::function<void()> m_handler;
public:
    [[nodiscard]]
    PropertyNotifier() = default;
    template<typename Functor>
    [[nodiscard]]
    PropertyNotifier(Functor handler)
        : PropertyObserver([](PropertyObserver *self, UntypedPropertyData *) {
            auto This = static_cast<PropertyNotifier *>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
    }

    template <typename Functor, typename Property,
             IsUntypedPropertyData<Property> = true>
    [[nodiscard]]
    PropertyNotifier(const Property &property, Functor handler)
        : PropertyObserver([](PropertyObserver *self, UntypedPropertyData *) {
            auto This = static_cast<PropertyNotifier *>(self);
            This->m_handler();
        })
        , m_handler(handler)
    {
        setSource(property);
    }
};


/**
 * @brief 主属性类
 *
 * Property 类是完整的属性类实现，它提供了值的读写、绑定管理和变化通知机制。
 *
 * 主要功能包括：
 * - 值的读写: value()/setValue()
 * - 绑定管理: setBinding()/takeBinding()
 * - 变化通知: onValueChanged()/subscribe()/addNotifier()
 *
 * @tparam T 属性值的类型
 */
template <typename T>
class Property : public PropertyData<T>
{
    PropertyBindingData d;

    bool is_equal(const T& v) const {
        if constexpr (TypeTraits::has_operator_equal_v<T>) {
            return v == this->val;
        }
        return false;
    }

public:
    using value_type = typename PropertyData<T>::value_type;
    using parameter_type = typename PropertyData<T>::parameter_type;
    using rvalue_ref = typename PropertyData<T>::rvalue_ref;
    using arrow_operator_result = typename PropertyData<T>::arrow_operator_result;

    Property() = default;
    explicit Property(parameter_type initialValue) : PropertyData<T>(initialValue) {}
    template<typename U> requires (PropertyData<T>::UseReferences && PropertyConcepts::ForwardConstructible<T, U>)
    explicit Property(U&& initialValue) : PropertyData<T>(std::forward<U>(initialValue)) {}
    explicit Property(const PropertyBinding<T> &binding)
        : Property()
    { setBinding(binding); }

    template <typename Functor> requires PropertyConcepts::InvocableReturning<Functor, T>
    explicit Property(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION)
        : Property(PropertyBinding<T>(std::forward<Functor>(f), location))
    {}

    parameter_type value() const
    {
        d.registerWithCurrentlyEvaluatingBinding();
        return this->val;
    }

    arrow_operator_result operator->() const
    {
        if constexpr (TypeTraits::is_dereferenceable_v<T>) {
            return value();
        } else if constexpr (std::is_pointer_v<T>) {
            value();
            return this->val;
        } else {
            return;
        }
    }

    parameter_type operator*() const
    {
        return value();
    }

    operator parameter_type() const
    {
        return value();
    }

    template<typename U = T>
    void setValue(U&& newValue, std::enable_if_t<PropertyData<T>::UseReferences && !std::is_same_v<std::decay_t<U>, parameter_type>, bool> = true)
    {
        d.removeBinding();
        if (is_equal(newValue))
            return;
        this->val = std::forward<U>(newValue);
        notify();
    }

    void setValue(parameter_type newValue)
    {
        d.removeBinding();
        if (is_equal(newValue))
            return;
        this->val = newValue;
        notify();
    }

    template<typename U = T>
    Property<T> &operator=(U&& newValue)
    {
        setValue(std::forward<U>(newValue));
        return *this;
    }

    PropertyBinding<T> setBinding(const PropertyBinding<T> &newBinding)
    {
        return PropertyBinding<T>(d.setBinding(newBinding, this));
    }

    bool setBinding(const UntypedPropertyBinding &newBinding)
    {
        setBinding(static_cast<const PropertyBinding<T> &>(newBinding));
        return true;
    }

    template <typename Functor>
    PropertyBinding<T> setBinding(Functor &&f,
                                   const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION,
                                   std::enable_if_t<std::is_invocable_v<Functor>> * = nullptr)
    {
        return setBinding(makePropertyBinding(std::forward<Functor>(f), location));
    }

    bool hasBinding() const { return d.hasBinding(); }


    PropertyBinding<T> binding() const
    {
        return PropertyBinding<T>(UntypedPropertyBinding(d.binding()));
    }

    PropertyBinding<T> takeBinding()
    {
        return PropertyBinding<T>(d.setBinding(UntypedPropertyBinding(), this));
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> onValueChanged(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        return PropertyChangeHandler<Functor>(*this, f);
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> subscribe(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        f();
        return onValueChanged(f);
    }

    template<typename Functor>
    PropertyNotifier addNotifier(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        return PropertyNotifier(*this, f);
    }

    const PropertyBindingData& bindingData() const { return d; }
private:
    void notify()
    {
        d.notifyObservers(this);
    }

    DISABLE_COPY_MOVE(Property)
};

/**
 * @brief 辅助函数
 *
 * 用于创建属性绑定的便利函数。
 */
template <typename PropertyType>
PropertyBinding<PropertyType> makePropertyBinding(const Property<PropertyType> &otherProperty,
                                                   const PropertyBindingSourceLocation &location =
                                                   PROPERTY_DEFAULT_BINDING_LOCATION)
{
    return makePropertyBinding([&otherProperty]() -> PropertyType { return otherProperty; }, location);
}

#endif // PROPERTY_H
