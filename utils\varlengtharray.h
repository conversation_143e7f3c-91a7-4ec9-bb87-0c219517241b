#ifndef VARLENGTHARRAY_H
#define VARLENGTHARRAY_H

#include <stdexcept>
#include <cassert>
#include <cstring>
#include <iterator>
#include <algorithm>
#include <initializer_list>
#include <iterator>
#include <concepts>
// #include <new>

#include <iostream>  // necessary
#include <memory>
#include <string.h>
#include <stdlib.h>

#include "global.h"
#include "types.h"
#include "typetraits_qt.h"

// TODO_SIMPLIFY: 添加缺失的工具函数定义，简化Qt依赖
template<typename T>
constexpr const T& Max(const T& a, const T& b) {
    return (a < b) ? b : a;
}

template<typename T>
constexpr const T& Min(const T& a, const T& b) {
    return (a < b) ? a : b;
}

#define ASSERT_X(condition, where, what) assert(condition)

// qcontainerfwd.h
constexpr sizetype VarLengthArrayDefaultPrealloc = 256;
template <typename T, sizetype Prealloc = VarLengthArrayDefaultPrealloc> class VarLengthArray;

#undef MaxAllocSize
[[maybe_unused]]
constexpr sizetype MaxAllocSize = (std::numeric_limits<sizetype>::max)();
// qcontainerfwd.h

// qhashfunctions.h
// workaround for a MSVC ICE,
// https://developercommunity.visualstudio.com/content/problem/996540/internal-compiler-error-on-msvc-1924-when-doing-sf.html
template <typename T>
inline constexpr bool NothrowHashableHelper_v = noexcept(qHash(std::declval<const T &>()));

template <typename T, typename Enable = void>
struct NothrowHashable : std::false_type {};

template <typename T>
struct NothrowHashable<T, std::enable_if_t<NothrowHashableHelper_v<T>>> : std::true_type {};
template <typename T>
constexpr inline bool NothrowHashable_v = NothrowHashable<T>::value;
// qhashfunctions.h

// qcontainertools_impl.h
template <typename T, typename N>
void q_uninitialized_move_if_noexcept_n(T* first, N n, T* out)
{
    if constexpr (std::is_nothrow_move_constructible_v<T> || !std::is_copy_constructible_v<T>)
        std::uninitialized_move_n(first, n, out);
    else
        std::uninitialized_copy_n(first, n, out);
}
template<typename T, typename Cmp = std::less<>>
static constexpr bool _points_into_range(const T *p, const T *b, const T *e,
                                          Cmp less = {}) noexcept
{
    return !less(p, b) && less(p, e);
}

template <typename C, typename T>
static constexpr bool _points_into_range(const T &p, const C &c) noexcept
{
    static_assert(std::is_same_v<decltype(std::data(c)), T>);

    // std::distance because QArrayDataPointer has a "qsizetype size"
    // member but no size() function
    return _points_into_range(p, std::data(c),
                               std::data(c) + std::distance(std::begin(c), std::end(c)));
}
// 现代化的迭代器concepts替换SFINAE
template <typename Iterator>
concept IsInputIterator = std::is_convertible_v<typename std::iterator_traits<Iterator>::iterator_category, std::input_iterator_tag>;

template <typename Iterator>
concept IsForwardIterator = std::is_convertible_v<typename std::iterator_traits<Iterator>::iterator_category, std::forward_iterator_tag>;

template <typename Iterator>
concept IsNotForwardIterator = !IsForwardIterator<Iterator>;

// 保持向后兼容的类型别名
template <typename Iterator>
using IfIsInputIterator = std::enable_if_t<IsInputIterator<Iterator>, bool>;
template <typename Iterator>
using IfIsForwardIterator = std::enable_if_t<IsForwardIterator<Iterator>, bool>;
template <typename Iterator>
using IfIsNotForwardIterator = std::enable_if_t<IsNotForwardIterator<Iterator>, bool>;
template <typename Container,
         typename InputIterator,
         IfIsNotForwardIterator<InputIterator> = true>
void reserveIfForwardIterator(Container *, InputIterator, InputIterator)
{
}

template <typename Container,
         typename ForwardIterator,
         IfIsForwardIterator<ForwardIterator> = true>
void reserveIfForwardIterator(Container *c, ForwardIterator f, ForwardIterator l)
{
    c->reserve(static_cast<typename Container::size_type>(std::distance(f, l)));
}
template <typename T, typename N>
void q_uninitialized_relocate_n(T* first, N n, T* out)
{
    if constexpr (TypeInfo<T>::isRelocatable) {
        static_assert(std::is_copy_constructible_v<T> || std::is_move_constructible_v<T>,
                      "Refusing to relocate this non-copy/non-move-constructible type.");
        if (n != N(0)) { // even if N == 0, out == nullptr or first == nullptr are UB for memcpy()
            std::memcpy(static_cast<void *>(out),
                        static_cast<const void *>(first),
                        n * sizeof(T));
        }
    } else {
        q_uninitialized_move_if_noexcept_n(first, n, out);
        if constexpr (TypeInfo<T>::isComplex)
            std::destroy_n(first, n);
    }
}

template <typename Container, typename Predicate>
auto sequential_erase_if(Container &c, Predicate &pred)
{
    // This is remove_if() modified to perform the find_if step on
    // const_iterators to avoid shared container detaches if nothing needs to
    // be removed. We cannot run remove_if after find_if: doing so would apply
    // the predicate to the first matching element twice!

    const auto cbegin = c.cbegin();
    const auto cend = c.cend();
    const auto t_it = std::find_if(cbegin, cend, pred);
    auto result = std::distance(cbegin, t_it);
    if (result == c.size())
        return result - result; // `0` of the right type

    // now detach:
    const auto e = c.end();

    auto it = std::next(c.begin(), result);
    auto dest = it;

    // Loop Invariants:
    // - it != e
    // - [next(it), e[ still to be checked
    // - [c.begin(), dest[ are result
    while (++it != e) {
        if (!pred(*it)) {
            *dest = std::move(*it);
            ++dest;
        }
    }

    result = std::distance(dest, e);
    c.erase(dest, e);
    return result;
}
template <typename Container, typename T>
auto sequential_erase_with_copy(Container &c, const T &t)
{
    using CopyProxy = std::conditional_t<std::is_copy_constructible_v<T>, T, const T &>;
    return sequential_erase(c, CopyProxy(t));
}

template <typename Container, typename T>
auto sequential_erase_one(Container &c, const T &t)
{
    const auto cend = c.cend();
    const auto it = std::find(c.cbegin(), cend, t);
    if (it == cend)
        return false;
    c.erase(it);
    return true;
}
template <typename T>
void q_rotate(T *first, T *mid, T *last)
{
    if constexpr (TypeInfo<T>::isRelocatable) {
        const auto cast = [](T *p) { return reinterpret_cast<unsigned char*>(p); };
        std::rotate(cast(first), cast(mid), cast(last));
    } else {
        std::rotate(first, mid, last);
    }
}
// qcontainertools_impl.h

template <size_t Size, size_t Align, sizetype Prealloc>
class VLAStorage
{
    template <size_t> class print;
protected:
    ~VLAStorage() = default;

    alignas(Align) char array[Prealloc * (Align > Size ? Align : Size)];
    // QT_WARNING_PUSH
        // QT_WARNING_DISABLE_DEPRECATED
        // ensure we maintain BC: std::aligned_storage_t was only specified by a
        // minimum size, but for BC we need the substitution to be exact in size:
        static_assert(std::is_same_v<print<sizeof(std::aligned_storage_t<Size, Align>[Prealloc])>,
                                     print<sizeof(array)>>);
    // QT_WARNING_POP
};

class VLABaseBase
{
protected:
    ~VLABaseBase() = default;

    sizetype a;      // capacity
    sizetype s;      // size
    void *ptr;     // data

    ALWAYS_INLINE constexpr void verify([[maybe_unused]] sizetype pos = 0,
                                          [[maybe_unused]] sizetype n = 1) const
    {
        assert(pos >= 0);
        assert(pos <= size());
        assert(n >= 0);
        assert(n <= size() - pos);
    }

    struct free_deleter {
        void operator()(void *p) const noexcept { free(p); }
    };
    using malloced_ptr = std::unique_ptr<void, free_deleter>;

public:
    using size_type = sizetype;

    constexpr size_type capacity() const noexcept { return a; }
    constexpr size_type size() const noexcept { return s; }
    constexpr bool empty() const noexcept { return size() == 0; }
};

template<class T>
class VLABase : public VLABaseBase
{
protected:
    ~VLABase() = default;

public:
    T *data() noexcept { return static_cast<T *>(ptr); }
    const T *data() const noexcept { return static_cast<T *>(ptr); }

    using iterator = T*;
    using const_iterator = const T*;

    iterator begin() noexcept { return data(); }
    const_iterator begin() const noexcept { return data(); }
    const_iterator cbegin() const noexcept { return begin(); }
    iterator end() noexcept { return data() + size(); }
    const_iterator end() const noexcept { return data() + size(); }
    const_iterator cend() const noexcept { return end(); }

    using reverse_iterator = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;

    reverse_iterator rbegin() noexcept { return reverse_iterator{end()}; }
    const_reverse_iterator rbegin() const noexcept { return const_reverse_iterator{end()}; }
    const_reverse_iterator crbegin() const noexcept { return rbegin(); }
    reverse_iterator rend() noexcept { return reverse_iterator{begin()}; }
    const_reverse_iterator rend() const noexcept { return const_reverse_iterator{begin()}; }
    const_reverse_iterator crend() const noexcept { return rend(); }

    using value_type = T;
    using reference = value_type&;
    using const_reference = const value_type&;
    using pointer = value_type*;
    using const_pointer = const value_type*;
    using difference_type = std::ptrdiff_t;

    reference front()
    {
        verify();
        return *begin();
    }

    const_reference front() const
    {
        verify();
        return *begin();
    }

    reference back()
    {
        verify();
        return *rbegin();
    }

    const_reference back() const
    {
        verify();
        return *rbegin();
    }

    void pop_back()
    {
        verify();
        if constexpr (TypeInfo<T>::isComplex)
            data()[size() - 1].~T();
        --s;
    }

    template <typename AT = T>
    sizetype indexOf(const AT &t, sizetype from = 0) const;
    template <typename AT = T>
    sizetype lastIndexOf(const AT &t, sizetype from = -1) const;
    template <typename AT = T>
    bool contains(const AT &t) const;

    reference operator[](sizetype idx)
    {
        verify(idx);
        return data()[idx];
    }
    const_reference operator[](sizetype idx) const
    {
        verify(idx);
        return data()[idx];
    }

    value_type value(sizetype i) const;
    value_type value(sizetype i, const T& defaultValue) const;

    void replace(sizetype i, const T &t);
    void remove(sizetype i, sizetype n = 1);
    template <typename AT = T>
    sizetype removeAll(const AT &t);
    template <typename AT = T>
    bool removeOne(const AT &t);
    template <typename Predicate>
    sizetype removeIf(Predicate pred);

    void clear()
    {
        if constexpr (TypeInfo<T>::isComplex)
            std::destroy_n(data(), size());
        s = 0;
    }

    iterator erase(const_iterator begin, const_iterator end);
    iterator erase(const_iterator pos) { return erase(pos, pos + 1); }

    static constexpr sizetype maxSize() noexcept
    {
        // -1 to deal with the pointer one-past-the-end
        return (MaxAllocSize / sizeof(T)) - 1;
    }
    constexpr sizetype max_size() const noexcept
    {
        return maxSize();
    }

    size_t hash(size_t seed) const noexcept(NothrowHashable_v<T>)
    {
        return qHashRange(begin(), end(), seed);
    }
protected:
    void growBy(sizetype prealloc, void *array, sizetype increment)
    { reallocate_impl(prealloc, array, size(), (std::max)(size() * 2, size() + increment)); }
    template <typename...Args>
    reference emplace_back_impl(sizetype prealloc, void *array, Args&&...args)
    {
        if (size() == capacity()) // ie. size() != 0
            growBy(prealloc, array, 1);
        reference r = *std::construct_at(end(), std::forward<Args>(args)...);
        ++s;
        return r;
    }
    template <typename...Args>
    iterator emplace_impl(sizetype prealloc, void *array, const_iterator pos, Args&&...arg);

    iterator insert_impl(sizetype prealloc, void *array, const_iterator pos, sizetype n, const T &t);

    template <typename S>
    bool equal(const VLABase<S> &other) const
    {
        return std::equal(begin(), end(), other.begin(), other.end());
    }
    template <typename S>
    bool less_than(const VLABase<S> &other) const
    {
        return std::lexicographical_compare(begin(), end(), other.begin(), other.end());
    }

    void append_impl(sizetype prealloc, void *array, const T *buf, sizetype n);
    void reallocate_impl(sizetype prealloc, void *array, sizetype size, sizetype alloc);
    void resize_impl(sizetype prealloc, void *array, sizetype sz, const T &v)
    {
        if (_points_into_range(&v, begin(), end())) {
            resize_impl(prealloc, array, sz, T(v));
            return;
        }
        reallocate_impl(prealloc, array, sz, Max(sz, capacity()));
        while (size() < sz) {
            std::construct_at(data() + size(), v);
            ++s;
        }
    }
    void resize_impl(sizetype prealloc, void *array, sizetype sz)
    {
        reallocate_impl(prealloc, array, sz, Max(sz, capacity()));
        if constexpr (TypeInfo<T>::isComplex) {
            // call default constructor for new objects (which can throw)
            while (size() < sz) {
                std::construct_at(data() + size());
                ++s;
            }
        } else {
            s = sz;
        }
    }

    void assign_impl(sizetype prealloc, void *array, sizetype n, const T &t);
    template <typename Iterator>
    void assign_impl(sizetype prealloc, void *array, Iterator first, Iterator last);

    bool isValidIterator(const const_iterator &i) const
    {
        const std::less<const T *> less = {};
        return !less(cend(), i) && !less(i, cbegin());
    }
};

/**
 * TODO_MODERNIZE: VarLengthArray std::span集成优化方案
 *
 * 当前状况：
 * - VarLengthArray提供传统的指针+大小访问模式
 * - 缺乏现代C++20的安全数组视图支持
 * - 数据访问接口可以进一步现代化
 *
 * 优化机会：
 * 1. 添加std::span接口：
 *    - span<T> as_span() noexcept { return {data(), size()}; }
 *    - span<const T> as_span() const noexcept { return {data(), size()}; }
 *
 * 2. ranges库集成：
 *    - 添加ranges::view支持
 *    - 实现ranges::sized_range concept
 *
 * 3. 安全性改进：
 *    - 边界检查的at()方法
 *    - 安全的子范围访问
 *
 * 技术收益：
 * - 更安全的数组访问，编译时边界检查
 * - 与标准库算法更好的集成
 * - 现代C++风格的API
 *
 * 实施风险：
 * - 需要C++20 std::span支持
 * - API扩展可能影响编译时间
 * - 需要仔细测试性能影响
 *
 * 推荐实施路径：
 * 1. 先添加as_span()方法，保持向后兼容
 * 2. 逐步添加ranges支持
 * 3. 最后考虑安全性改进
 */

// Prealloc = 256 by default, specified in qcontainerfwd.h
template<class T, sizetype Prealloc>
class VarLengthArray
// #if QT_VERSION >= QT_VERSION_CHECK(7,0,0) || defined(QT_BOOTSTRAPPED)
    // : public VLAStorage<sizeof(T), alignof(T), Prealloc>,
    //   public VLABase<T>
// #else
    : public VLABase<T>,
      public VLAStorage<sizeof(T), alignof(T), Prealloc>
// #endif
{
    template <class S, sizetype Prealloc2>
    friend class VarLengthArray;
    using Base = VLABase<T>;
    using Storage = VLAStorage<sizeof(T), alignof(T), Prealloc>;
    static_assert(Prealloc > 0, "VarLengthArray Prealloc must be greater than 0.");
    static_assert(std::is_nothrow_destructible_v<T>, "Types with throwing destructors are not supported in Qt containers.");
    using Base::verify;

    // 现代化的类型约束，保持SFINAE兼容性
    template <typename U>
    using if_copyable = std::enable_if_t<std::is_copy_constructible_v<U>, bool>;
    template <typename InputIterator>
    using if_input_iterator = IfIsInputIterator<InputIterator>;
public:
    static constexpr sizetype PreallocatedSize = Prealloc;

    using size_type = typename Base::size_type;
    using value_type = typename Base::value_type;
    using pointer = typename Base::pointer;
    using const_pointer = typename Base::const_pointer;
    using reference = typename Base::reference;
    using const_reference = typename Base::const_reference;
    using difference_type = typename Base::difference_type;

    using iterator = typename Base::iterator;
    using const_iterator = typename Base::const_iterator;
    using reverse_iterator = typename Base::reverse_iterator;
    using const_reverse_iterator = typename Base::const_reverse_iterator;

    VarLengthArray() noexcept
    {
        this->a = Prealloc;
        this->s = 0;
        this->ptr = this->array;
    }

    inline explicit VarLengthArray(sizetype size);

#ifndef Q_QDOC
    template <typename U = T, if_copyable<U> = true>
#endif
    explicit VarLengthArray(sizetype sz, const T &v)
        : VarLengthArray{}
    {
        resize(sz, v);
    }

    VarLengthArray(const VarLengthArray &other)
        : VarLengthArray{}
    {
        append(other.constData(), other.size());
    }

    VarLengthArray(VarLengthArray &&other)
        noexcept(std::is_nothrow_move_constructible_v<T>)
        : Base(other)
    {
        const auto otherInlineStorage = reinterpret_cast<T*>(other.array);
        if (data() == otherInlineStorage) {
            // inline buffer - move into our inline buffer:
            this->ptr = this->array;
            q_uninitialized_relocate_n(otherInlineStorage, size(), data());
        } else {
            // heap buffer - we just stole the memory
        }
        // reset other to internal storage:
        other.a = Prealloc;
        other.s = 0;
        other.ptr = otherInlineStorage;
    }

    VarLengthArray(std::initializer_list<T> args)
        : VarLengthArray(args.begin(), args.end())
    {
    }

    template <typename InputIterator, if_input_iterator<InputIterator> = true>
    inline VarLengthArray(InputIterator first, InputIterator last)
        : VarLengthArray()
    {
        reserveIfForwardIterator(this, first, last);
        std::copy(first, last, std::back_inserter(*this));
    }

    inline ~VarLengthArray()
    {
        if constexpr (TypeInfo<T>::isComplex)
            std::destroy_n(data(), size());
        if (data() != reinterpret_cast<T *>(this->array))
            free(data());
    }
    inline VarLengthArray<T, Prealloc> &operator=(const VarLengthArray<T, Prealloc> &other)
    {
        if (this != &other) {
            clear();
            append(other.constData(), other.size());
        }
        return *this;
    }

    VarLengthArray &operator=(VarLengthArray &&other)
        noexcept(std::is_nothrow_move_constructible_v<T>)
    {
        // we're only required to be self-move-assignment-safe
        // when we're in the moved-from state (Hinnant criterion)
        // the moved-from state is the empty state, so we're good with the clear() here:
        clear();
        assert(capacity() >= Prealloc);
        const auto otherInlineStorage = other.array;
        if (other.ptr != otherInlineStorage) {
            // heap storage: steal the external buffer, reset other to otherInlineStorage
            this->a = std::exchange(other.a, Prealloc);
            this->ptr = std::exchange(other.ptr, otherInlineStorage);
        } else {
            // inline storage: move into our storage (doesn't matter whether inline or external)
            q_uninitialized_relocate_n(other.data(), other.size(), data());
        }
        this->s = std::exchange(other.s, 0);
        return *this;
    }

    VarLengthArray<T, Prealloc> &operator=(std::initializer_list<T> list)
    {
        assign(list);
        return *this;
    }

    inline void removeLast()
    {
        Base::pop_back();
    }
#ifdef Q_QDOC
    inline sizetype size() const { return this->s; }
    static constexpr sizetype maxSize() noexcept { return VLABase<T>::maxSize(); }
    constexpr sizetype max_size() const noexcept { return VLABase<T>::max_size(); }
#endif
    using Base::size;
    using Base::max_size;
    inline sizetype count() const { return size(); }
    inline sizetype length() const { return size(); }
    inline T &first()
    {
        return front();
    }
    inline const T &first() const
    {
        return front();
    }
    T &last()
    {
        return back();
    }
    const T &last() const
    {
        return back();
    }
    bool isEmpty() const { return empty(); }
    void resize(sizetype sz) { Base::resize_impl(Prealloc, this->array, sz); }
#ifndef Q_QDOC
    template <typename U = T, if_copyable<U> = true>
#endif
    void resize(sizetype sz, const T &v)
    { Base::resize_impl(Prealloc, this->array, sz, v); }
    using Base::clear;
#ifdef Q_QDOC
    inline void clear() { resize(0); }
#endif
    void squeeze() { reallocate(size(), size()); }

    using Base::capacity;
#ifdef Q_QDOC
    sizetype capacity() const { return this->a; }
#endif
    void reserve(sizetype sz) { if (sz > capacity()) reallocate(size(), sz); }

#ifdef Q_QDOC
    template <typename AT = T>
    inline sizetype indexOf(const AT &t, sizetype from = 0) const;
    template <typename AT = T>
    inline sizetype lastIndexOf(const AT &t, sizetype from = -1) const;
    template <typename AT = T>
    inline bool contains(const AT &t) const;
#endif
    using Base::indexOf;
    using Base::lastIndexOf;
    using Base::contains;

#ifdef Q_QDOC
    inline T &operator[](sizetype idx)
    {
        verify(idx);
        return data()[idx];
    }
    inline const T &operator[](sizetype idx) const
    {
        verify(idx);
        return data()[idx];
    }
#endif
    using Base::operator[];
    inline const T &at(sizetype idx) const { return operator[](idx); }

#ifdef Q_QDOC
    T value(sizetype i) const;
    T value(sizetype i, const T &defaultValue) const;
#endif
    using Base::value;

    inline void append(const T &t)
    {
        if (size() == capacity())
            emplace_back(T(t));
        else
            emplace_back(t);
    }

    void append(T &&t)
    {
        emplace_back(std::move(t));
    }

    void append(const T *buf, sizetype sz)
    { Base::append_impl(Prealloc, this->array, buf, sz); }
    inline VarLengthArray<T, Prealloc> &operator<<(const T &t)
    { append(t); return *this; }
    inline VarLengthArray<T, Prealloc> &operator<<(T &&t)
    { append(std::move(t)); return *this; }
    inline VarLengthArray<T, Prealloc> &operator+=(const T &t)
    { append(t); return *this; }
    inline VarLengthArray<T, Prealloc> &operator+=(T &&t)
    { append(std::move(t)); return *this; }

    void insert(sizetype i, T &&t);
    void insert(sizetype i, const T &t);
    void insert(sizetype i, sizetype n, const T &t);

    VarLengthArray &assign(sizetype n, const T &t)
    { Base::assign_impl(Prealloc, this->array, n, t); return *this; }
    template <typename InputIterator, if_input_iterator<InputIterator> = true>
    VarLengthArray &assign(InputIterator first, InputIterator last)
    { Base::assign_impl(Prealloc, this->array, first, last); return *this; }
    VarLengthArray &assign(std::initializer_list<T> list)
    { assign(list.begin(), list.end()); return *this; }

#ifdef Q_QDOC
    void replace(sizetype i, const T &t);
    void remove(sizetype i, sizetype n = 1);
    template <typename AT = T>
    sizetype removeAll(const AT &t);
    template <typename AT = T>
    bool removeOne(const AT &t);
    template <typename Predicate>
    sizetype removeIf(Predicate pred);
#endif
    using Base::replace;
    using Base::remove;
    using Base::removeAll;
    using Base::removeOne;
    using Base::removeIf;

#ifdef Q_QDOC
    inline T *data() { return this->ptr; }
    inline const T *data() const { return this->ptr; }
#endif
    using Base::data;
    inline const T *constData() const { return data(); }
#ifdef Q_QDOC
    inline iterator begin() { return data(); }
    inline const_iterator begin() const { return data(); }
    inline const_iterator cbegin() const { return begin(); }
    inline const_iterator constBegin() const { return begin(); }
    inline iterator end() { return data() + size(); }
    inline const_iterator end() const { return data() + size(); }
    inline const_iterator cend() const { return end(); }
#endif

    using Base::begin;
    using Base::cbegin;
    auto constBegin() const -> const_iterator { return begin(); }
    using Base::end;
    using Base::cend;
    inline const_iterator constEnd() const { return end(); }
#ifdef Q_QDOC
    reverse_iterator rbegin() { return reverse_iterator(end()); }
    reverse_iterator rend() { return reverse_iterator(begin()); }
    const_reverse_iterator rbegin() const { return const_reverse_iterator(end()); }
    const_reverse_iterator rend() const { return const_reverse_iterator(begin()); }
    const_reverse_iterator crbegin() const { return const_reverse_iterator(end()); }
    const_reverse_iterator crend() const { return const_reverse_iterator(begin()); }
#endif
    using Base::rbegin;
    using Base::crbegin;
    using Base::rend;
    using Base::crend;

    iterator insert(const_iterator before, sizetype n, const T &x)
    { return Base::insert_impl(Prealloc, this->array, before, n, x); }
    iterator insert(const_iterator before, T &&x) { return emplace(before, std::move(x)); }
    inline iterator insert(const_iterator before, const T &x) { return insert(before, 1, x); }
#ifdef Q_QDOC
    iterator erase(const_iterator begin, const_iterator end);
    inline iterator erase(const_iterator pos) { return erase(pos, pos + 1); }
#endif
    using Base::erase;

// STL compatibility:
#ifdef Q_QDOC
    inline bool empty() const { return isEmpty(); }
#endif
    using Base::empty;
    inline void push_back(const T &t) { append(t); }
    void push_back(T &&t) { append(std::move(t)); }
#ifdef Q_QDOC
    inline void pop_back() { removeLast(); }
    inline T &front() { return first(); }
    inline const T &front() const { return first(); }
    inline T &back() { return last(); }
    inline const T &back() const { return last(); }
#endif
    using Base::pop_back;
    using Base::front;
    using Base::back;
    void shrink_to_fit() { squeeze(); }
    template <typename...Args>
    iterator emplace(const_iterator pos, Args &&...args)
    { return Base::emplace_impl(Prealloc, this->array, pos, std::forward<Args>(args)...); }
    template <typename...Args>
    T &emplace_back(Args &&...args)
    { return Base::emplace_back_impl(Prealloc, this->array, std::forward<Args>(args)...); }


#ifdef Q_QDOC
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator==(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator!=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator< (const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator> (const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator<=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
    template <typename T, sizetype Prealloc1, sizetype Prealloc2>
    friend inline bool operator>=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r);
#else
    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_eq_result<U> operator==(const VarLengthArray<T, Prealloc> &l, const VarLengthArray<T, Prealloc2> &r)
    {
        return l.equal(r);
    }

    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_eq_result<U> operator!=(const VarLengthArray<T, Prealloc> &l, const VarLengthArray<T, Prealloc2> &r)
    {
        return !(l == r);
    }

    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_lt_result<U> operator<(const VarLengthArray<T, Prealloc> &lhs, const VarLengthArray<T, Prealloc2> &rhs)
        noexcept(noexcept(std::lexicographical_compare(lhs.begin(), lhs.end(),
                                                       rhs.begin(), rhs.end())))
    {
        return lhs.less_than(rhs);
    }

    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_lt_result<U> operator>(const VarLengthArray<T, Prealloc> &lhs, const VarLengthArray<T, Prealloc2> &rhs)
        noexcept(noexcept(lhs < rhs))
    {
        return rhs < lhs;
    }

    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_lt_result<U> operator<=(const VarLengthArray<T, Prealloc> &lhs, const VarLengthArray<T, Prealloc2> &rhs)
        noexcept(noexcept(lhs < rhs))
    {
        return !(lhs > rhs);
    }

    template <typename U = T, sizetype Prealloc2 = Prealloc> friend
        TypeTraits::compare_lt_result<U> operator>=(const VarLengthArray<T, Prealloc> &lhs, const VarLengthArray<T, Prealloc2> &rhs)
        noexcept(noexcept(lhs < rhs))
    {
        return !(lhs < rhs);
    }
#endif

private:
    template <typename U, sizetype Prealloc2>
    bool equal(const VarLengthArray<U, Prealloc2> &other) const
    { return Base::equal(other); }
    template <typename U, sizetype Prealloc2>
    bool less_than(const VarLengthArray<U, Prealloc2> &other) const
    { return Base::less_than(other); }

    void reallocate(sizetype sz, sizetype alloc)
    { Base::reallocate_impl(Prealloc, this->array, sz, alloc); }

    using Base::isValidIterator;
};

template <typename InputIterator,
         typename ValueType = typename std::iterator_traits<InputIterator>::value_type,
         IfIsInputIterator<InputIterator> = true>
VarLengthArray(InputIterator, InputIterator) -> VarLengthArray<ValueType>;

template <class T, sizetype Prealloc>
/*Q_INLINE_TEMPLATE*/ inline VarLengthArray<T, Prealloc>::VarLengthArray(sizetype asize)
    : VarLengthArray()
{
    ASSERT_X(asize >= 0, "VarLengthArray::VarLengthArray(sizetype)",
               "Size must be greater than or equal to 0.");

    // historically, this ctor worked for non-copyable/non-movable T, so keep it working, why not?
    // resize(asize) // this requires a movable or copyable T, can't use, need to do it by hand

    if (asize > Prealloc) {
        this->ptr = malloc(asize * sizeof(T));
        Q_CHECK_PTR(this->ptr);
        this->a = asize;
    }
    if constexpr (TypeInfo<T>::isComplex)
        std::uninitialized_default_construct_n(data(), asize);
    this->s = asize;
}

template <class T>
template <typename AT>
/*Q_INLINE_TEMPLATE*/ inline sizetype VLABase<T>::indexOf(const AT &t, sizetype from) const
{
    if (from < 0)
        from = Max(from + size(), sizetype(0));
    if (from < size()) {
        const T *n = data() + from - 1;
        const T *e = end();
        while (++n != e)
            if (*n == t)
                return n - data();
    }
    return -1;
}

template <class T>
template <typename AT>
/*Q_INLINE_TEMPLATE*/ inline sizetype VLABase<T>::lastIndexOf(const AT &t, sizetype from) const
{
    if (from < 0)
        from += size();
    else if (from >= size())
        from = size() - 1;
    if (from >= 0) {
        const T *b = begin();
        const T *n = b + from + 1;
        while (n != b) {
            if (*--n == t)
                return n - b;
        }
    }
    return -1;
}

template <class T>
template <typename AT>
/*Q_INLINE_TEMPLATE*/ inline bool VLABase<T>::contains(const AT &t) const
{
    const T *b = begin();
    const T *i = end();
    while (i != b) {
        if (*--i == t)
            return true;
    }
    return false;
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ void VLABase<T>::append_impl(sizetype prealloc, void *array, const T *abuf, sizetype increment)
{
    assert(abuf || increment == 0);
    if (increment <= 0)
        return;

    const sizetype asize = size() + increment;

    if (asize >= capacity())
        growBy(prealloc, array, increment);

    if constexpr (TypeInfo<T>::isComplex)
        std::uninitialized_copy_n(abuf, increment, end());
    else
        memcpy(static_cast<void *>(end()), static_cast<const void *>(abuf), increment * sizeof(T));

    this->s = asize;
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ void VLABase<T>::assign_impl(sizetype prealloc, void *array, sizetype n, const T &t)
{
    assert(n >= 0);
    if (n > capacity()) {
        reallocate_impl(prealloc, array, 0, capacity()); // clear
        resize_impl(prealloc, array, n, t);
    } else {
        auto mid = (std::min)(n, size());
        std::fill(data(), data() + mid, t);
        std::uninitialized_fill(data() + mid, data() + n, t);
        s = n;
        erase(data() + n, data() + size());
    }
}

template <class T>
template <typename Iterator>
/*Q_OUTOFLINE_TEMPLATE*/ void VLABase<T>::assign_impl(sizetype prealloc, void *array, Iterator first, Iterator last)
{
    // This function only provides the basic exception guarantee.
    constexpr bool IsFwdIt =
        std::is_convertible_v<typename std::iterator_traits<Iterator>::iterator_category,
                              std::forward_iterator_tag>;
    if constexpr (IsFwdIt) {
        const sizetype n = std::distance(first, last);
        if (n > capacity())
            reallocate_impl(prealloc, array, 0, n); // clear & reserve n
    }

    auto dst = begin();
    const auto dend = end();
    while (true) {
        if (first == last) {          // ran out of elements to assign
            std::destroy(dst, dend);
            break;
        }
        if (dst == dend) {            // ran out of existing elements to overwrite
            if constexpr (IsFwdIt) {
                dst = std::uninitialized_copy(first, last, dst);
                break;
            } else {
                do {
                    emplace_back_impl(prealloc, array, *first);
                } while (++first != last);
                return; // size() is already correct (and dst invalidated)!
            }
        }
        *dst = *first;                // overwrite existing element
        ++dst;
        ++first;
    }
    this->s = dst - begin();
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ void VLABase<T>::reallocate_impl(sizetype prealloc, void *array, sizetype asize, sizetype aalloc)
{
    assert(aalloc >= asize);
    assert(data());
    T *oldPtr = data();
    sizetype osize = size();

    const sizetype copySize = Min(asize, osize);
    assert(copySize >= 0);

    if (aalloc != capacity()) {
        VLABaseBase::malloced_ptr guard;
        void *newPtr;
        sizetype newA;
        if (aalloc > prealloc) {
            newPtr = malloc(aalloc * sizeof(T));
            guard.reset(newPtr);
            //Q_CHECK_PTR(newPtr); // could throw
            if (!newPtr) {
                throw std::runtime_error("Q_CHECK_PTR: varlenghtarry.h: nullptr");
            }
            // by design: in case of QT_NO_EXCEPTIONS malloc must not fail or it crashes here
            newA = aalloc;
        } else {
            newPtr = array;
            newA = prealloc;
        }
        q_uninitialized_relocate_n(oldPtr, copySize,
                                              reinterpret_cast<T *>(newPtr));
        // commit:
        ptr = newPtr;
        guard.release();
        a = newA;
    }
    s = copySize;

    // destroy remaining old objects
    if constexpr (TypeInfo<T>::isComplex) {
        if (osize > asize)
            std::destroy(oldPtr + asize, oldPtr + osize);
    }

    if (oldPtr != reinterpret_cast<T *>(array) && oldPtr != data())
        free(oldPtr);
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ T VLABase<T>::value(sizetype i) const
{
    if (size_t(i) >= size_t(size()))
        return T();
    return operator[](i);
}
template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ T VLABase<T>::value(sizetype i, const T &defaultValue) const
{
    return (size_t(i) >= size_t(size())) ? defaultValue : operator[](i);
}

template <class T, sizetype Prealloc>
inline void VarLengthArray<T, Prealloc>::insert(sizetype i, T &&t)
{ verify(i, 0);
    insert(cbegin() + i, std::move(t)); }
template <class T, sizetype Prealloc>
inline void VarLengthArray<T, Prealloc>::insert(sizetype i, const T &t)
{ verify(i, 0);
    insert(begin() + i, 1, t); }
template <class T, sizetype Prealloc>
inline void VarLengthArray<T, Prealloc>::insert(sizetype i, sizetype n, const T &t)
{ verify(i, 0);
    insert(begin() + i, n, t); }
template <class T>
inline void VLABase<T>::remove(sizetype i, sizetype n)
{ verify(i, n);
    erase(begin() + i, begin() + i + n); }
template <class T>
template <typename AT>
inline sizetype VLABase<T>::removeAll(const AT &t)
{ return sequential_erase_with_copy(*this, t); }
template <class T>
template <typename AT>
inline bool VLABase<T>::removeOne(const AT &t)
{ return sequential_erase_one(*this, t); }
template <class T>
template <typename Predicate>
inline sizetype VLABase<T>::removeIf(Predicate pred)
{ return sequential_erase_if(*this, pred); }

template <class T>
inline void VLABase<T>::replace(sizetype i, const T &t)
{
    verify(i);
    data()[i] = t;
}

template <class T>
template <typename...Args>
/*Q_OUTOFLINE_TEMPLATE*/ auto VLABase<T>::emplace_impl(sizetype prealloc, void *array, const_iterator before, Args &&...args) -> iterator
{
    assert_X(isValidIterator(before), "VarLengthArray::insert", "The specified const_iterator argument 'before' is invalid");
    assert(size() <= capacity());
    assert(capacity() > 0);

    const sizetype offset = sizetype(before - cbegin());
    emplace_back_impl(prealloc, array, std::forward<Args>(args)...);
    const auto b = begin() + offset;
    const auto e = end();
    q_rotate(b, e - 1, e);
    return b;
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ auto VLABase<T>::insert_impl(sizetype prealloc, void *array, const_iterator before, sizetype n, const T &t) -> iterator
{
    assert_X(isValidIterator(before), "VarLengthArray::insert", "The specified const_iterator argument 'before' is invalid");

    const sizetype offset = sizetype(before - cbegin());
    resize_impl(prealloc, array, size() + n, t);
    const auto b = begin() + offset;
    const auto e = end();
    q_rotate(b, e - n, e);
    return b;
}

template <class T>
/*Q_OUTOFLINE_TEMPLATE*/ auto VLABase<T>::erase(const_iterator abegin, const_iterator aend) -> iterator
{
    assert_X(isValidIterator(abegin), "VarLengthArray::erase", "The specified const_iterator argument 'abegin' is invalid");
    assert_X(isValidIterator(aend), "VarLengthArray::erase", "The specified const_iterator argument 'aend' is invalid");

    sizetype f = sizetype(abegin - cbegin());
    sizetype l = sizetype(aend - cbegin());
    sizetype n = l - f;

    if (n == 0) // avoid UB in std::move() below
        return data() + f;

    assert(n > 0); // aend must be reachable from abegin

    if constexpr (!TypeInfo<T>::isRelocatable) {
        std::move(begin() + l, end(), QT_MAKE_CHECKED_ARRAY_ITERATOR(begin() + f, size() - f));
        std::destroy(end() - n, end());
    } else {
        std::destroy(abegin, aend);
        memmove(static_cast<void *>(data() + f), static_cast<const void *>(data() + l), (size() - l) * sizeof(T));
    }
    this->s -= n;
    return data() + f;
}

#ifdef Q_QDOC
// Fake definitions for qdoc, only the redeclaration is used.
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator==(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator!=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator< (const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator> (const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator<=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
template <typename T, sizetype Prealloc1, sizetype Prealloc2>
bool operator>=(const VarLengthArray<T, Prealloc1> &l, const VarLengthArray<T, Prealloc2> &r)
{ return bool{}; }
#endif

template <typename T, sizetype Prealloc>
size_t qHash(const VarLengthArray<T, Prealloc> &key, size_t seed = 0)
    noexcept(NothrowHashable_v<T>)
{
    return key.hash(seed);
}

template <typename T, sizetype Prealloc, typename AT>
sizetype erase(VarLengthArray<T, Prealloc> &array, const AT &t)
{
    return array.removeAll(t);
}

template <typename T, sizetype Prealloc, typename Predicate>
sizetype erase_if(VarLengthArray<T, Prealloc> &array, Predicate pred)
{
    return array.removeIf(pred);
}

#endif // VARLENGTHARRAY_H
