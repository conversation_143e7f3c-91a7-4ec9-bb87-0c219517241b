﻿#ifndef METATYPE_H
#define METATYPE_H

#include <type_traits>

#include "typetraits.h"

// #if __cplusplus < 202002L
// using char8_t = unsigned char;
// namespace std {
// using u8string = string;
// }
// #endif

// using Nullptr = std::nullptr_t;
// // std::is_integral_v
// using Char = char;
// using SChar = signed char;
// using UChar = unsigned char;
// using WChar = wchar_t;
// using Char8 = char8_t;
// using Char16 = char16_t;
// using Char32 = char32_t;
// using Bool = bool;
// using Short = short;
// using UShort = unsigned short;
// using Int = int;
// using UInt = unsigned int;
// using Long = long;
// using ULong = unsigned long;
// using LongLong = long long;
// using ULongLong = unsigned long long;
// // std::is_floating_point_v
// using Float = float;
// using Double = double;
// using LongDouble = long double;

using String = std::string;
// using WString = std::wstring;
// using U8String = std::u8string;
// using U16String = std::u16string;
// using U32String = std::u32string;

/**
 * TODO_SIMPLIFY: MetaType系统现代化重构方案
 *
 * 当前架构问题：
 * - 基于枚举的类型系统，不够类型安全
 * - fromType()方法实现为空，缺乏实际功能
 * - 与现代C++的type_index、type_info机制重复
 * - 大量注释掉的类型定义，设计不够清晰
 *
 * 现代化收益：
 * - 使用std::type_index提供更好的类型安全性
 * - 利用consteval和concepts优化编译时类型处理
 * - 减少运行时类型检查开销
 * - 更好的模板友好性
 *
 * 重构方案：
 * 1. 轻量级方案：保留MetaType但用std::type_index实现
 * 2. 现代化方案：完全用std::type_index + concepts替换
 * 3. 混合方案：保留枚举用于基础类型，复杂类型用type_index
 *
 * 技术考虑：
 * - 需要评估性能影响（编译时 vs 运行时）
 * - 考虑序列化和反序列化需求
 * - 评估与现有绑定系统的集成复杂度
 *
 * 推荐方案：先实施方案1，逐步向方案2迁移
 */
class MetaType {
public:
    enum Type {
        UnknownType, //
        Void,        //	void
        Bool,        //	bool
        Char,        // char
        SChar,       // signed char
        UChar,       // unsigned char
        WChar,       // wchar_t
        Char8,       // char8_t
        Char16,      // char16_t
        Char32,      // char32_t
        Short,       // short;
        UShort,      // unsigned short;
        Int,         //	int
        UInt,        //	unsigned int
        Long,        //	long
        ULong,       //	unsigned long
        LongLong,    //	long long
        ULongLong,   //	unsigned long long
        Float,       //	float
        Double,      //	double
        LongDouble,  //	long double

        Nullptr,     //	std::nullptr_t
        VoidStar,    //	void *

        UserType     //
    };

    constexpr static MetaType fromType();

    template<typename T>
    constexpr static MetaType fromType()
    {
        return MetaType();
    }
    size_t id() const { return 0;}
};

template <typename T>
inline constexpr int MetaTypeId()
{
    return MetaType::fromType<T>().id();
}

struct MetaTypeInfo {
    const char* name;
    int typeId;
};

class Variant;

/**
 * TODO_MODERNIZE: View类std::string_view现代化方案
 *
 * 当前状况：
 * - View类使用std::string进行字符串操作
 * - 构造函数和操作符重载创建临时字符串对象
 * - 缺乏现代C++20字符串视图优化
 *
 * 优化机会：
 * 1. 构造函数现代化：
 *    - View(std::string_view str) : m_str(str) {}
 *    - 减少临时字符串创建
 *
 * 2. 操作符重载优化：
 *    - View operator+(std::string_view other) const
 *    - void operator+=(std::string_view other)
 *
 * 3. 接口扩展：
 *    - std::string_view view() const noexcept
 *    - 提供零拷贝字符串访问
 *
 * 技术收益：
 * - 减少内存分配和字符串复制
 * - 更好的性能，特别是字符串字面量处理
 * - 与现代C++字符串处理API一致
 *
 * 实施风险：
 * - 需要仔细处理生命周期问题
 * - API变更可能影响现有代码
 * - 需要确保字符串视图的有效性
 *
 * 推荐实施路径：
 * 1. 添加string_view构造函数重载
 * 2. 逐步替换操作符重载
 * 3. 添加view()访问方法
 */
class View {
public:
    View() = default;
    View(std::string str) : m_str(str) {}
    View(const char* str) : m_str(str) {}
    operator std::string() const { return m_str; }
    View operator+(const View& other) const { return View(m_str + other.m_str); }
    View operator+(const char* other) const { return View(m_str + std::string(other)); }
    View operator+(const std::string& other) const { return View(m_str + other); }
    void operator+=(const View& other) { m_str += other.m_str; }
    void operator+=(const char* other) { m_str += std::string(other); }
    void operator+=(const std::string& other) { m_str += other; }

private:
    std::string m_str;
};

template <typename T>
std::string _typename() {
    if constexpr (std::is_same_v<T, const char*> || std::is_same_v<T, char*>)
        return "chars";
    if constexpr (std::is_same_v<T, const signed char*> || std::is_same_v<T, signed char*>)
        return "signed chars";
    if constexpr (std::is_same_v<T, const unsigned char*> || std::is_same_v<T, unsigned char*>)
        return "unsigned chars";
    if constexpr (std::is_same_v<T, const wchar_t*> || std::is_same_v<T, wchar_t*>)
        return "wchars";
    // if constexpr (std::is_same_v<T, const char8_t*> || std::is_same_v<T, char8_t*>)
    //     return "char8s";
    if constexpr (std::is_same_v<T, const char16_t*> || std::is_same_v<T, char16_t*>)
        return "char16s";
    if constexpr (std::is_same_v<T, const char32_t*> || std::is_same_v<T, char32_t*>)
        return "char32s";
    if constexpr (std::is_same_v<T, char const*>)
        return "char const*";
    if constexpr (is_string_v<T>)
        return "string";
    if constexpr (is_vector_v<T>)
        return "vector(" + _typename<typename T::value_type>() + ")";
    if constexpr (is_map_v<T>)
        return "map";
    if constexpr (is_unordered_map_v<T>)
        return "hash";
    if constexpr (is_pair_v<T>)
        return "pair";
    if constexpr (is_set_v<T>)
        return "set";
    if constexpr (std::is_same_v<T, Variant>)
        return "Variant";
    return typeid(T).name();
}

#endif // METATYPE_H
