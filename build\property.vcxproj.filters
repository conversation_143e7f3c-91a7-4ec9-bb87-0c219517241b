﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="E:\Projects\Code\AI Agent\property\property.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="E:\Projects\Code\AI Agent\property\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\property.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\propertyprivate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\property_p.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\utils\shareddata.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\utils\taggedpointer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="E:\Projects\Code\AI Agent\property\utils\varlengtharray.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\Projects\Code\AI Agent\property\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{4FF00F2D-617F-3933-910E-0504551419DB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{828B1D60-FCC7-38B6-8BBA-90802C7279F7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
