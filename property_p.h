#ifndef PROPERTY_P_H
#define PROPERTY_P_H

#include "property.h"
#include "global.h"
#include "varlengtharray.h"
// TODO_REMOVE: bindingstorage.h include已移除 - BindingStorage类已不存在
// #include "bindingstorage.h"
#include <array>

// TODO_SIMPLIFY: HANDLE类型定义 - 从bindingstorage.h移动过来
typedef void * HANDLE;  // 原来在bindingstorage.h中定义

// 前向声明
struct BindingEvaluationState;
struct CompatPropertySafePoint;
struct PropertyDelayedNotifications;

/**
 * TODO_SIMPLIFY: BindingStatus全局状态管理
 *
 * BindingStatus从BindingStorage中提取出来，用于管理全局绑定状态。
 * 这是属性绑定系统的核心状态管理结构，负责：
 * - 跟踪当前正在评估的绑定
 * - 管理兼容属性的安全点
 * - 处理属性更新组的延迟通知
 *
 * 移动原因：BindingStorage被移除，但BindingStatus仍被Property<T>系统使用
 */
struct BindingStatus
{
    BindingEvaluationState *currentlyEvaluatingBinding = nullptr;
    CompatPropertySafePoint *currentCompatProperty = nullptr;
    HANDLE threadId = nullptr;
    PropertyDelayedNotifications *groupUpdateData = nullptr;
};

// TODO_SIMPLIFY: ScopedValueRollback类 - 简化的RAII工具类，用于自动恢复值
template <typename T>
class ScopedValueRollback
{
public:
    [[nodiscard]]
    explicit constexpr ScopedValueRollback(T &var)
        : varRef(var), oldValue(var)
    {
    }

    [[nodiscard]]
    explicit constexpr ScopedValueRollback(T &var, const T& newValue)
        : varRef(var), oldValue(var)
    {
        varRef = newValue;
    }

#if __cpp_constexpr >= 201907L
    constexpr
#endif
    ~ScopedValueRollback()
    {
        varRef = std::move(oldValue);
    }

    constexpr void commit()
    {
        oldValue = varRef;
    }

private:
    T &varRef;
    T oldValue;

    DISABLE_COPY_MOVE(ScopedValueRollback)
};

// TODO_REMOVE: 未使用的函数声明和宏定义 - 核心API调用链中未使用
/*
bool isAnyBindingEvaluating();

struct BindingStatusAccessToken {};

#define QT_MOVE_ASSIGNMENT_OPERATOR_IMPL_VIA_MOVE_AND_SWAP(Class) \
Class &operator=(Class &&other) noexcept { \
        Class moved(std::move(other)); \
        swap(moved); \
        return *this; \
}
#define QT_MOVE_ASSIGNMENT_OPERATOR_IMPL_VIA_PURE_SWAP(Class) \
Class &operator=(Class &&other) noexcept { \
        swap(other); \
        return *this; \
}
*/

struct BindingObserverPtr
{
private:
    PropertyObserver *d = nullptr;
public:
    BindingObserverPtr() = default;
    DISABLE_COPY(BindingObserverPtr);
    void swap(BindingObserverPtr &other) noexcept
    { std::swap(d, other.d); }
    BindingObserverPtr(BindingObserverPtr &&other) noexcept : d(std::exchange(other.d, nullptr)) {}
    // TODO_SIMPLIFY: 可简化为标准移动赋值实现，移除宏依赖
    BindingObserverPtr &operator=(BindingObserverPtr &&other) noexcept {
        BindingObserverPtr moved(std::move(other));
        swap(moved);
        return *this;
    }

    inline BindingObserverPtr(PropertyObserver *observer) noexcept;
    inline ~BindingObserverPtr();
    inline PropertyBindingPrivate *binding() const noexcept;
    inline PropertyObserver *operator ->();
};

using PendingBindingObserverList = VarLengthArray<BindingObserverPtr>;

struct PropertyBindingDataPointer
{
    const PropertyBindingData *ptr = nullptr;

    PropertyBindingPrivate *binding() const
    {
        return ptr->binding();
    }

    void setObservers(PropertyObserver *observer)
    {
        auto &d = ptr->d_ref();
        observer->prev = reinterpret_cast<PropertyObserver**>(&d);
        d = reinterpret_cast<std::uintptr_t>(observer);
    }
    static void fixupAfterMove(PropertyBindingData *ptr);
    void ALWAYS_INLINE addObserver(PropertyObserver *observer);
    inline void setFirstObserver(PropertyObserver *observer);
    inline PropertyObserverPointer firstObserver() const;
    static PropertyProxyBindingData *proxyData(PropertyBindingData *ptr);

    inline int observerCount() const;

    template <typename T>
    static PropertyBindingDataPointer get(Property<T> &property)
    {
        return PropertyBindingDataPointer{&property.bindingData()};
    }
};


struct PropertyObserverNodeProtector
{
    DISABLE_COPY_MOVE(PropertyObserverNodeProtector)

    PropertyObserverBase m_placeHolder;
    [[nodiscard]]
    PropertyObserverNodeProtector(PropertyObserver *observer)
    {
        // insert m_placeholder after observer into the linked list
        PropertyObserver *next = observer->next.data();
        m_placeHolder.next = next;
        observer->next = static_cast<PropertyObserver *>(&m_placeHolder);
        if (next)
            next->prev = &m_placeHolder.next;
        m_placeHolder.prev = &observer->next;
        m_placeHolder.next.setTag(PropertyObserver::ObserverIsPlaceholder);
    }

    PropertyObserver *next() const { return m_placeHolder.next.data(); }

    ~PropertyObserverNodeProtector();
};


// This is a helper "namespace"
struct PropertyObserverPointer
{
    PropertyObserver *ptr = nullptr;

    void unlink()
    {
        unlink_common();
    }

    void unlink_fast()
    {
        unlink_common();
    }

    void setBindingToNotify(PropertyBindingPrivate *binding)
    {
        assert(ptr->next.tag() != PropertyObserver::ObserverIsPlaceholder);
        ptr->binding = binding;
        ptr->next.setTag(PropertyObserver::ObserverNotifiesBinding);
    }

    void setBindingToNotify_unsafe(PropertyBindingPrivate *binding);
    void setChangeHandler(PropertyObserver::ChangeHandler changeHandler);

    enum class Notify {Everything, OnlyChangeHandlers};

    void notify(UntypedPropertyData *propertyDataPtr);
#ifndef QT_NO_DEBUG
    void noSelfDependencies(PropertyBindingPrivate *binding);
#else
    void noSelfDependencies(PropertyBindingPrivate *) {}
#endif
    void evaluateBindings(PendingBindingObserverList &bindingObservers, BindingStatus *status);
    void observeProperty(PropertyBindingDataPointer property);

    explicit operator bool() const { return ptr != nullptr; }

    PropertyObserverPointer nextObserver() const { return {ptr->next.data()}; }

    PropertyBindingPrivate *binding() const
    {
        std::cout << ptr->next.tag() << std::endl;
        assert(ptr->next.tag() == PropertyObserver::ObserverNotifiesBinding);
        return ptr->binding;
    }

private:
    void unlink_common()
    {
        if (ptr->next)
            ptr->next->prev = ptr->prev;
        if (ptr->prev)
            ptr->prev.setPointer(ptr->next.data());
        ptr->next = nullptr;
        ptr->prev.clear();
    }
};

// TODO_REMOVE: PropertyBindingErrorPrivate类已移除 - 简化了PropertyBindingError的pImpl模式
/*
class PropertyBindingErrorPrivate : public SharedData
{
public:
    PropertyBindingError::Type type = PropertyBindingError::NoError;
    String description;
};
*/

// TODO_REMOVE: 重复的BindingStatus定义已移除 - 已在文件开头定义

struct BindingEvaluationState
{
    BindingEvaluationState(PropertyBindingPrivate *binding, BindingStatus *status);
    ~BindingEvaluationState()
    {
        *currentState = previousState;
    }

    PropertyBindingPrivate *binding;
    BindingEvaluationState *previousState = nullptr;
    BindingEvaluationState **currentState = nullptr;
    VarLengthArray<const PropertyBindingData *, 8> alreadyCaptureProperties;
};

struct CompatPropertySafePoint
{
    CompatPropertySafePoint(BindingStatus *status, UntypedPropertyData *property);
    ~CompatPropertySafePoint()
    {
        *currentState = previousState;
        *currentlyEvaluatingBindingList = bindingState;
    }
    UntypedPropertyData *property;
    CompatPropertySafePoint *previousState = nullptr;
    CompatPropertySafePoint **currentState = nullptr;
    BindingEvaluationState **currentlyEvaluatingBindingList = nullptr;
    BindingEvaluationState *bindingState = nullptr;
};

// TODO_REMOVE: CurrentCompatPropertyThief类 - 仅用于Qt兼容性，核心API未使用
/*
struct CurrentCompatPropertyThief
{
    DISABLE_COPY_MOVE(CurrentCompatPropertyThief)
public:
    CurrentCompatPropertyThief(BindingStatus *status)
        : status(&status->currentCompatProperty)
        , stolen(std::exchange(status->currentCompatProperty, nullptr))
    {
    }

    ~CurrentCompatPropertyThief()
    {
        *status = stolen;
    }

private:
    CompatPropertySafePoint **status = nullptr;
    CompatPropertySafePoint *stolen = nullptr;
};
*/


class PropertyBindingPrivate : public RefCounted
{
private:
    friend struct PropertyBindingDataPointer;
    friend class PropertyBindingPrivatePtr;

    using ObserverArray = std::array<PropertyObserver, 4>;

private:

    // used to detect binding loops for lazy evaluated properties
    bool updating = false;
    bool hasStaticObserver = false;
    bool pendingNotify = false;
    bool hasBindingWrapper:1;
    // used to detect binding loops for eagerly evaluated properties
    bool isQQmlPropertyBinding:1;
    /* a sticky binding does not get removed in removeBinding
       this is used to support QQmlPropertyData::DontRemoveBinding
       in qtdeclarative
    */
    bool m_sticky:1;

    const BindingFunctionVTable *vtable;

    union {
        PropertyObserverCallback staticObserverCallback = nullptr;
        PropertyBindingWrapper staticBindingWrapper;
    };
    ObserverArray inlineDependencyObservers; // for things we are observing

    PropertyObserverPointer firstObserver; // list of observers observing us
    std::unique_ptr<std::vector<PropertyObserver>> heapObservers; // for things we are observing

protected:
    UntypedPropertyData *propertyDataPtr = nullptr;

    /* For bindings set up from C++, location stores where the binding was created in the C++ source
       For QQmlPropertyBinding that information does not make sense, and the location in the QML  file
       is stored somewhere else. To make efficient use of the space, we instead provide a scratch space
       for QQmlPropertyBinding (which stores further binding information there).
       Anything stored in the union must be trivially destructible.
       (checked in qproperty.cpp)
    */
    using DeclarativeErrorCallback = void(*)(PropertyBindingPrivate *);
    union {
        PropertyBindingSourceLocation location;
        struct {
            std::byte declarativeExtraData[sizeof(PropertyBindingSourceLocation) - sizeof(DeclarativeErrorCallback)];
            DeclarativeErrorCallback errorCallBack;
        };
    };
private:
    PropertyBindingError error;

public:
    static constexpr size_t getSizeEnsuringAlignment() {
        constexpr auto align = alignof (std::max_align_t) - 1;
        constexpr size_t sizeEnsuringAlignment = (sizeof(PropertyBindingPrivate) + align) & ~align;
        static_assert (sizeEnsuringAlignment % alignof (std::max_align_t) == 0,
                      "Required for placement new'ing the function behind it.");
        return sizeEnsuringAlignment;
    }


    // public because the auto-tests access it, too.
    size_t dependencyObserverCount = 0;

    bool isUpdating() {return updating;}
    void setSticky(bool keep = true) {m_sticky = keep;}
    bool isSticky() {return m_sticky;}
    void scheduleNotify() {pendingNotify = true;}

    PropertyBindingPrivate(const BindingFunctionVTable *vtable,
                            const PropertyBindingSourceLocation &location, bool isQQmlPropertyBinding=false)
        : hasBindingWrapper(false)
        , isQQmlPropertyBinding(isQQmlPropertyBinding)
        , m_sticky(false)
        , vtable(vtable)
        , location(location)
    {}
    ~PropertyBindingPrivate();


    void setProperty(UntypedPropertyData *propertyPtr) { propertyDataPtr = propertyPtr; }
    void setStaticObserver(PropertyObserverCallback callback, PropertyBindingWrapper bindingWrapper)
    {
        assert(!(callback && bindingWrapper));
        if (callback) {
            hasStaticObserver = true;
            hasBindingWrapper = false;
            staticObserverCallback = callback;
        } else if (bindingWrapper) {
            hasStaticObserver = false;
            hasBindingWrapper = true;
            staticBindingWrapper = bindingWrapper;
        } else {
            hasStaticObserver = false;
            hasBindingWrapper = false;
            staticObserverCallback = nullptr;
        }
    }
    void prependObserver(PropertyObserverPointer observer)
    {
        observer.ptr->prev = const_cast<PropertyObserver **>(&firstObserver.ptr);
        firstObserver = observer;
    }

    PropertyObserverPointer takeObservers()
    {
        auto observers = firstObserver;
        firstObserver.ptr = nullptr;
        return observers;
    }

    void clearDependencyObservers();

    ALWAYS_INLINE PropertyObserverPointer allocateDependencyObserver() {
        if (dependencyObserverCount < inlineDependencyObservers.size()) {
            ++dependencyObserverCount;
            return {&inlineDependencyObservers[dependencyObserverCount - 1]};
        }
        return allocateDependencyObserver_slow();
    }

    PropertyObserverPointer allocateDependencyObserver_slow();

    PropertyBindingSourceLocation sourceLocation() const
    {
        if (!hasCustomVTable())
            return this->location;
        PropertyBindingSourceLocation location;
        constexpr auto msg = "Custom location";
        location.fileName = msg;
        return location;
    }
    PropertyBindingError bindingError() const { return error; }


    void unlinkAndDeref();

    bool evaluateRecursive(PendingBindingObserverList &bindingObservers, BindingStatus *status = nullptr);

    bool ALWAYS_INLINE evaluateRecursive_inline(PendingBindingObserverList &bindingObservers, BindingStatus *status);

    void notifyNonRecursive(const PendingBindingObserverList &bindingObservers);
    enum NotificationState : bool { Delayed, Sent };
    NotificationState notifyNonRecursive();

    static PropertyBindingPrivate *get(const UntypedPropertyBinding &binding)
    { return static_cast<PropertyBindingPrivate *>(binding.d.data()); }

    void setError(PropertyBindingError &&e)
    { error = std::move(e); }

    void detachFromProperty()
    {
        hasStaticObserver = false;
        hasBindingWrapper = false;
        propertyDataPtr = nullptr;
        clearDependencyObservers();
    }

    static PropertyBindingPrivate *currentlyEvaluatingBinding();

    bool hasCustomVTable() const
    {
        return vtable->size == 0;
    }

    static void destroyAndFreeMemory(PropertyBindingPrivate *priv) {
        if (priv->hasCustomVTable()) {
            // special hack for QQmlPropertyBinding which has a
            // different memory layout than normal PropertyBindings
            priv->vtable->destroy(priv);
        } else{
            priv->~PropertyBindingPrivate();
            delete[] reinterpret_cast<std::byte *>(priv);
        }
    }
};


inline void PropertyBindingDataPointer::setFirstObserver(PropertyObserver *observer)
{
    if (auto *b = binding()) {
        b->firstObserver.ptr = observer;
        return;
    }
    auto &d = ptr->d_ref();
    d = reinterpret_cast<std::uintptr_t>(observer);
}

inline void PropertyBindingDataPointer::fixupAfterMove(PropertyBindingData *ptr)
{
    auto &d = ptr->d_ref();
    if (ptr->isNotificationDelayed()) {
        PropertyProxyBindingData *proxy = ptr->proxyData();
        assert(proxy);
        proxy->originalBindingData = ptr;
    }
    // If PropertyBindingData has been moved, and it has an observer
    // we have to adjust the firstObserver's prev pointer to point to
    // the moved to PropertyBindingData's d_ptr
    if (d & PropertyBindingData::BindingBit)
        return; // nothing to do if the observer is stored in the binding
    if (auto observer = reinterpret_cast<PropertyObserver *>(d))
        observer->prev = reinterpret_cast<PropertyObserver **>(&d);
}

inline PropertyObserverPointer PropertyBindingDataPointer::firstObserver() const
{
    if (auto *b = binding())
        return b->firstObserver;
    return { reinterpret_cast<PropertyObserver *>(ptr->d()) };
}

inline PropertyProxyBindingData *PropertyBindingDataPointer::proxyData(PropertyBindingData *ptr)
{
    if (!ptr->isNotificationDelayed())
        return nullptr;
    return ptr->proxyData();
}

inline int PropertyBindingDataPointer::observerCount() const
{
    int count = 0;
    for (auto observer = firstObserver(); observer; observer = observer.nextObserver())
        ++count;
    return count;
}

bool isPropertyInBindingWrapper(const UntypedPropertyData *property);
void initBindingStatusThreadId();


// TODO_REMOVE: ObjectCompatProperty类 - 完整的Qt对象兼容性属性类，核心API未使用
/*
template<typename Class, typename T, auto Offset, auto Setter, auto Signal = nullptr,
         auto Getter = nullptr>
class ObjectCompatProperty : public PropertyData<T>
{
    template<typename Property, typename>
    friend class BindableInterfaceForProperty;

    using ThisType = ObjectCompatProperty<Class, T, Offset, Setter, Signal, Getter>;
    using SignalTakesValue = std::is_invocable<decltype(Signal), Class, T>;
    Class *owner()
    {
        char *that = reinterpret_cast<char *>(this);
        return reinterpret_cast<Class *>(that - detail::getOffset(Offset));
    }
    const Class *owner() const
    {
        char *that = const_cast<char *>(reinterpret_cast<const char *>(this));
        return reinterpret_cast<Class *>(that - detail::getOffset(Offset));
    }

    static bool bindingWrapper(UntypedPropertyData *dataPtr, PropertyBindingFunction binding)
    {
        auto *thisData = static_cast<ThisType *>(dataPtr);
        BindingStorage *storage = qGetBindingStorage(thisData->owner());
        PropertyData<T> copy;
        {
            CurrentCompatPropertyThief thief(storage->bindingStatus);
            binding.vtable->call(&copy, binding.functor);
            if constexpr (has_operator_equal_v<T>)
                if (copy.valueBypassingBindings() == thisData->valueBypassingBindings())
                    return false;
        }
        // ensure value and setValue know we're currently evaluating our binding
        CompatPropertySafePoint guardThis(storage->bindingStatus, thisData);
        (thisData->owner()->*Setter)(copy.valueBypassingBindings());
        return true;
    }
    bool inBindingWrapper(const BindingStorage *storage) const
    {
        return storage->bindingStatus && storage->bindingStatus->currentCompatProperty
               && isPropertyInBindingWrapper(this);
    }

    inline static T getPropertyValue(const UntypedPropertyData *d) {
        auto prop = static_cast<const ThisType *>(d);
        if constexpr (std::is_null_pointer_v<decltype(Getter)>)
            return prop->value();
        else
            return (prop->owner()->*Getter)();
    }

public:
    using value_type = typename PropertyData<T>::value_type;
    using parameter_type = typename PropertyData<T>::parameter_type;
    using arrow_operator_result = typename PropertyData<T>::arrow_operator_result;

    ObjectCompatProperty() = default;
    explicit ObjectCompatProperty(const T &initialValue) : PropertyData<T>(initialValue) {}
    explicit ObjectCompatProperty(T &&initialValue) : PropertyData<T>(std::move(initialValue)) {}

    parameter_type value() const
    {
        const BindingStorage *storage = qGetBindingStorage(owner());
        // make sure we don't register this binding as a dependency to itself
        if (storage->bindingStatus && storage->bindingStatus->currentlyEvaluatingBinding && !inBindingWrapper(storage))
            storage->registerDependency_helper(this);
        return this->val;
    }

    arrow_operator_result operator->() const
    {
        if constexpr (is_dereferenceable_v<T>) {
            return value();
        } else if constexpr (std::is_pointer_v<T>) {
            value();
            return this->val;
        } else {
            return;
        }
    }

    parameter_type operator*() const
    {
        return value();
    }

    operator parameter_type() const
    {
        return value();
    }

    void setValue(parameter_type t)
    {
        BindingStorage *storage = qGetBindingStorage(owner());
        if (auto *bd = storage->bindingData(this)) {
            // make sure we don't remove the binding if called from the bindingWrapper
            if (bd->hasBinding() && !inBindingWrapper(storage))
                bd->removeBinding_helper();
        }
        this->val = t;
    }

    ObjectCompatProperty &operator=(parameter_type newValue)
    {
        setValue(newValue);
        return *this;
    }

    PropertyBinding<T> setBinding(const PropertyBinding<T> &newBinding)
    {
        PropertyBindingData *bd = qGetBindingStorage(owner())->bindingData(this, true);
        UntypedPropertyBinding oldBinding(bd->setBinding(newBinding, this, nullptr, bindingWrapper));
        // notification is already handled in PropertyBindingData::setBinding
        return static_cast<PropertyBinding<T> &>(oldBinding);
    }

    bool setBinding(const UntypedPropertyBinding &newBinding)
    {
        if (!newBinding.isNull() && false) // newBinding.valueMetaType() != MetaType::fromType<T>())
            return false;
        setBinding(static_cast<const PropertyBinding<T> &>(newBinding));
        return true;
    }

    template <typename Functor>
    PropertyBinding<T> setBinding(Functor &&f,
                                   const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION,
                                   std::enable_if_t<std::is_invocable_v<Functor>> * = nullptr)
    {
        return setBinding(makePropertyBinding(std::forward<Functor>(f), location));
    }


    bool hasBinding() const {
        auto *bd = qGetBindingStorage(owner())->bindingData(this);
        return bd && bd->binding() != nullptr;
    }

    void removeBindingUnlessInWrapper()
    {
        BindingStorage *storage = qGetBindingStorage(owner());
        if (auto *bd = storage->bindingData(this)) {
            // make sure we don't remove the binding if called from the bindingWrapper
            if (bd->hasBinding() && !inBindingWrapper(storage))
                bd->removeBinding_helper();
        }
    }

    void notify()
    {
        BindingStorage *storage = qGetBindingStorage(owner());
        if (auto bd = storage->bindingData(this, false)) {
            // This partly duplicates PropertyBindingData::notifyObservers because we want to
            // check for inBindingWrapper() after checking for isNotificationDelayed() and
            // firstObserver. This is because inBindingWrapper() is the most expensive check.
            if (!bd->isNotificationDelayed()) {
                PropertyBindingDataPointer d{bd};
                if (PropertyObserverPointer observer = d.firstObserver()) {
                    if (!inBindingWrapper(storage)) {
                        PendingBindingObserverList bindingObservers;
                        if (bd->notifyObserver_helper(this, storage, observer, bindingObservers)
                            == PropertyBindingData::Evaluated) {
                            // evaluateBindings() can trash the observers. We need to re-fetch here.
                            if (PropertyObserverPointer observer = d.firstObserver())
                                observer.notify(this);
                            for (auto&& bindingObserver: bindingObservers)
                                bindingObserver.binding()->notifyNonRecursive();
                        }
                    }
                }
            }
        }
        if constexpr (!std::is_null_pointer_v<decltype(Signal)>) {
            if constexpr (SignalTakesValue::value)
                (owner()->*Signal)(getPropertyValue(this));
            else
                (owner()->*Signal)();
        }
    }

    PropertyBinding<T> binding() const
    {
        auto *bd = qGetBindingStorage(owner())->bindingData(this);
        return static_cast<PropertyBinding<T> &&>(QUntypedPropertyBinding(bd ? bd->binding() : nullptr));
    }

    PropertyBinding<T> takeBinding()
    {
        return setBinding(PropertyBinding<T>());
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> onValueChanged(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        return PropertyChangeHandler<Functor>(*this, f);
    }

    template<typename Functor>
    PropertyChangeHandler<Functor> subscribe(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        f();
        return onValueChanged(f);
    }

    template<typename Functor>
    PropertyNotifier addNotifier(Functor f)
    {
        static_assert(std::is_invocable_v<Functor>, "Functor callback must be callable without any parameters");
        return PropertyNotifier(*this, f);
    }

    PropertyBindingData &bindingData() const
    {
        auto *storage = const_cast<BindingStorage *>(qGetBindingStorage(owner()));
        return *storage->bindingData(const_cast<ObjectCompatProperty *>(this), true);
    }
};
*/

// TODO_REMOVE: BindableInterfaceForProperty特化 - 与ObjectCompatProperty相关，核心API未使用
/*
// template<typename Class, typename Ty, auto Offset, auto Setter, auto Signal, auto Getter>
// class BindableInterfaceForProperty<
//     ObjectCompatProperty<Class, Ty, Offset, Setter, Signal, Getter>, std::void_t<Class>>
// {
//     using Property = ObjectCompatProperty<Class, Ty, Offset, Setter, Signal, Getter>;
//     using T = typename Property::value_type;
// public:
//     static constexpr BindableInterface iface = {
//         [](const UntypedPropertyData *d, void *value) -> void
//         { *static_cast<T*>(value) = Property::getPropertyValue(d); },
//         [](UntypedPropertyData *d, const void *value) -> void
//         {
//             (static_cast<Property *>(d)->owner()->*Setter)(*static_cast<const T*>(value));
//         },
//         [](const UntypedPropertyData *d) -> UntypedPropertyBinding
//         { return static_cast<const Property *>(d)->binding(); },
//         [](UntypedPropertyData *d, const UntypedPropertyBinding &binding) -> UntypedPropertyBinding
//         { return static_cast<Property *>(d)->setBinding(static_cast<const PropertyBinding<T> &>(binding)); },
//         [](const UntypedPropertyData *d, const PropertyBindingSourceLocation &location) -> UntypedPropertyBinding
//         { return makePropertyBinding([d]() -> T { return Property::getPropertyValue(d); }, location); },
//         [](const UntypedPropertyData *d, PropertyObserver *observer) -> void
//         { observer->setSource(static_cast<const Property *>(d)->bindingData()); },
//         []() { return MetaType::fromType<T>(); }
//     };
// };
*/

// TODO_REMOVE: 绑定状态挂起/恢复函数 - 核心API调用链中未使用
/*
BindingEvaluationState *suspendCurrentBindingStatus();
void restoreBindingStatus(BindingEvaluationState *status);
*/

inline bool PropertyBindingPrivate::evaluateRecursive_inline(PendingBindingObserverList &bindingObservers, BindingStatus *status)
{
    if (updating) {
        error = PropertyBindingError(PropertyBindingError::BindingLoop, "Recursive binding evaluation detected");
        if (isQQmlPropertyBinding)
            errorCallBack(this);
        return false;
    }

    PropertyBindingPrivatePtr keepAlive {this};

    ScopedValueRollback<bool> updateGuard(updating, true);

    BindingEvaluationState evaluationFrame(this, status);

    auto bindingFunctor =  reinterpret_cast<std::byte *>(this) +
                          PropertyBindingPrivate::getSizeEnsuringAlignment();
    bool changed = false;
    if (hasBindingWrapper) {
        changed = staticBindingWrapper(propertyDataPtr,
                                       {vtable, bindingFunctor});
    } else {
        changed = vtable->call(propertyDataPtr, bindingFunctor);
    }
    // If there was a change, we must set pendingNotify.
    // If there was not, we must not clear it, as that only should happen in notifyRecursive
    pendingNotify = pendingNotify || changed;
    if (!changed || !firstObserver)
        return changed;

    firstObserver.noSelfDependencies(this);
    firstObserver.evaluateBindings(bindingObservers, status);
    return true;
}

inline void PropertyObserverPointer::notify(UntypedPropertyData *propertyDataPtr)
{
    auto observer = const_cast<PropertyObserver*>(ptr);

    while (observer) {
        PropertyObserver *next = observer->next.data();
        switch (PropertyObserver::ObserverTag(observer->next.tag())) {
        case PropertyObserver::ObserverNotifiesChangeHandler:
        {
            auto handlerToCall = observer->changeHandler;
            // prevent recursion
            if (next && next->next.tag() == PropertyObserver::ObserverIsPlaceholder) {
                observer = next->next.data();
                continue;
            }
            // handlerToCall might modify the list
            PropertyObserverNodeProtector protector(observer);
            handlerToCall(observer, propertyDataPtr);
            next = protector.next();
            break;
        }
        case PropertyObserver::ObserverNotifiesBinding:
            break;
        case PropertyObserver::ObserverIsPlaceholder:
            // recursion is already properly handled somewhere else
            break;
                default: assert(false && "Unreachable code reached!");
        }
        observer = next;
    }
}


inline PropertyObserverNodeProtector::~PropertyObserverNodeProtector()
{
    PropertyObserverPointer d{static_cast<PropertyObserver *>(&m_placeHolder)};
    d.unlink_fast();
}

BindingObserverPtr::BindingObserverPtr(PropertyObserver *observer) noexcept : d(observer)
{
    assert(d);
    PropertyObserverPointer{d}.binding()->addRef();
}

BindingObserverPtr::~BindingObserverPtr()
{
    if (!d)
        return;

    PropertyBindingPrivate *bindingPrivate = binding();
    if (!bindingPrivate->deref())
        PropertyBindingPrivate::destroyAndFreeMemory(bindingPrivate);
}

PropertyBindingPrivate *BindingObserverPtr::binding() const noexcept { return PropertyObserverPointer{d}.binding(); }

PropertyObserver *BindingObserverPtr::operator->() { return d; }



#endif // PROPERTY_P_H
