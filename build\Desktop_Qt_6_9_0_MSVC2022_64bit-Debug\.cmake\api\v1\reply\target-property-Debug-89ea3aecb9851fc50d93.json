{"artifacts": [{"path": "property.exe"}, {"path": "property.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 21, "parent": 0}, {"command": 1, "file": 0, "line": 23, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd"}], "includes": [{"backtrace": 2, "path": "E:/Projects/Code/AI Agent/property"}, {"backtrace": 2, "path": "E:/Projects/Code/AI Agent/property/global"}, {"backtrace": 2, "path": "E:/Projects/Code/AI Agent/property/global/types"}, {"backtrace": 2, "path": "E:/Projects/Code/AI Agent/property/utils"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [3, 7]}], "id": "property::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "property", "nameOnDisk": "property.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 4, 5, 6]}, {"name": "Source Files", "sourceIndexes": [3, 7]}], "sources": [{"backtrace": 1, "path": "property.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "propertyprivate.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "property_p.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "property.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "utils/shareddata.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "utils/taggedpointer.h", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "utils/varlengtharray.h", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}